<template>
  <div class="app">
    <!-- Enhanced Header -->
    <header class="app-header">
      <div class="container">
        <div class="flex items-center justify-between py-lg">
          <div class="app-title">
            <h1 class="text-3xl font-bold">Simonitor</h1>
            <p class="text-muted">Advanced Sims 4 Mod Management & Intelligence</p>
          </div>
          <div class="app-actions flex items-center gap-sm">
            <button
              class="btn btn-primary btn-sm"
              @click="selectAndAnalyzeFolder"
              :disabled="isAnalyzing"
            >
              <FolderOpenIcon class="w-4 h-4" />
              Analyze Mods Folder
            </button>
            <button
              class="btn btn-secondary btn-sm"
              @click="loadTestData"
              style="margin-left: 8px;"
            >
              Load Test Data
            </button>
            <button
              v-if="analysisResults.length > 0"
              class="btn btn-secondary btn-sm"
              @click="exportResults"
            >
              <ArrowDownTrayIcon class="w-4 h-4" />
              Export
            </button>
            <button class="btn btn-secondary btn-sm" @click="openSettings">
              <CogIcon class="w-4 h-4" />
              Settings
            </button>
            <span class="badge badge-info">v2.0.0</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="app-main">


      <!-- Debug Info -->
      <div v-if="showDebugMode" style="background: #1a1a1a; color: #00ff00; padding: 10px; margin: 10px; border-radius: 5px; font-family: monospace;">
        <h3>🐛 Debug Info</h3>
        <p>Analysis Results Length: {{ analysisResults.length }}</p>
        <p>Is Analyzing: {{ isAnalyzing }}</p>
        <p>Current Mods Folder: {{ currentModsFolder }}</p>
        <p>Analysis Error: {{ analysisError }}</p>
        <details v-if="analysisResults.length > 0">
          <summary>First Analysis Result:</summary>
          <pre>{{ JSON.stringify(analysisResults[0], null, 2) }}</pre>
        </details>
      </div>

      <!-- Enhanced ModDashboard Interface -->
      <ModDashboard
        :mods="analysisResults"
        :is-loading="isAnalyzing"
        :show-debug-mode="showDebugMode"
      />

      <!-- Error Display -->
      <div v-if="analysisError" class="container">
        <section class="error-section mb-xl">
          <div class="card">
            <div class="error-content">
              <div class="flex items-center gap-md mb-md">
                <ExclamationTriangleIcon class="w-6 h-6 text-error" />
                <h3 class="font-medium text-error">Analysis Error</h3>
              </div>
              <p class="text-muted mb-md">{{ analysisError }}</p>
              <button class="btn btn-secondary btn-sm" @click="clearError">
                Dismiss
              </button>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- Footer -->
    <footer class="app-footer">
      <div class="container">
        <div class="text-center py-md text-sm text-muted">
          <p>Simonitor - Built with ❤️ for the Sims 4 community</p>
        </div>
      </div>
    </footer>

    <!-- Settings Modal -->
    <Modal
      :is-open="isSettingsOpen"
      title="Settings"
      @close="closeSettings"
    >
      <AppSettings
        @settings-changed="handleSettingsChanged"
        ref="settingsRef"
      />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import {
  FolderOpenIcon,
  ArrowDownTrayIcon,
  CogIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

import ModDashboard from './components/ModDashboard.vue';
import FileUpload from './components/FileUpload.vue';
import Modal from './components/Modal.vue';
import AppSettings from './components/AppSettings.vue';

// Types
import type { AnalyzedPackage, ResourceInfo } from '../types/analysis';

// Reactive state
const selectedFiles = ref<File[]>([]);
const analysisResults = ref<any[]>([]);
const isAnalyzing = ref(false);

// Test data for debugging
const testMods = ref([
  {
    fileName: 'Test Mod 1.package',
    fileType: 'Package',
    fileSize: 1024000,
    author: 'Test Author',
    version: '1.0',
    modName: 'Test Mod 1',

    fileExtension: '.package'
  },
  {
    fileName: 'Test Mod 2.ts4script',
    fileType: 'Script',
    fileSize: 512000,
    author: 'Another Author',
    version: '2.1',
    modName: 'Test Mod 2',

    fileExtension: '.ts4script'
  }
]);
const analyzedCount = ref(0);
const analysisError = ref<string | null>(null);
const fileUploadRef = ref();
const isSettingsOpen = ref(false);
const settingsRef = ref();
const showDebugMode = ref(true); // Temporarily enabled for debugging // Disabled for production-ready interface
const currentModsFolder = ref<string>('');

// Watch for analysis results changes
watch(analysisResults, (newResults, oldResults) => {
  console.log('🔄 App.vue - analysisResults WATCH triggered:', {
    newLength: newResults?.length || 0,
    oldLength: oldResults?.length || 0,
    timestamp: new Date().toISOString()
  });
}, { immediate: true, deep: true });

// Watch for isAnalyzing changes
watch(isAnalyzing, (newAnalyzing, oldAnalyzing) => {
  // Loading state changed - trigger reactivity
}, { immediate: true });

// Event handlers
function handleFilesSelected(files: File[]) {
  console.log('🔄 App.vue - handleFilesSelected called:', { filesLength: files.length });
  selectedFiles.value = files;
  // Clear previous results when new files are selected
  if (files.length === 0) {
    console.log('🔄 App.vue - CLEARING analysisResults in handleFilesSelected');
    analysisResults.value = [];
    analysisError.value = null;
  }
}

async function handleAnalyzeRequested(files: File[]) {
  if (files.length === 0) return;

  if (!window.electronAPI) {
    analysisError.value = 'Electron API not available. Please run in Electron app.';
    return;
  }

  isAnalyzing.value = true;
  analyzedCount.value = 0;
  console.log('🔄 App.vue - CLEARING analysisResults in handleAnalyzeRequested');
  analysisResults.value = [];
  analysisError.value = null;

  try {
    // Analyze files one by one
    for (const file of files) {
      const filePath = (file as any).path;
      
      // Create a promise to handle the async analysis
      const analysisPromise = new Promise<AnalyzedPackage>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Analysis timeout'));
        }, 30000); // 30 second timeout
        
        const handleResult = (result: any) => {
          clearTimeout(timeout);
          window.electronAPI.offAnalysisResult?.(handleResult);
          
          if (result.error) {
            reject(new Error(result.error));
          } else {
            resolve(result);
          }
        };
        
        window.electronAPI.onAnalysisResult(handleResult);
        window.electronAPI.analyzePackage(filePath);
      });
      
      try {
        const result = await analysisPromise;
        analysisResults.value.push(result);
        analyzedCount.value++;
      } catch (error) {
        console.error(`Error analyzing ${file.name}:`, error);
        analysisError.value = `Error analyzing ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        break;
      }
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'An unexpected error occurred';
  } finally {
    isAnalyzing.value = false;
  }
}

function clearError() {
  analysisError.value = null;
}

function loadTestData() {
  isAnalyzing.value = false;
  analysisResults.value = [...testMods.value];
}

// Settings handlers
function openSettings() {
  isSettingsOpen.value = true;
}

function closeSettings() {
  isSettingsOpen.value = false;
}

function handleSettingsChanged(settings: any) {
  // Handle settings changes - could be used to update app behavior
  if (settings.debugMode !== undefined) {
    showDebugMode.value = settings.debugMode;
  }
}

// New enhanced methods for folder analysis
async function selectAndAnalyzeFolder() {
  try {
    if (!window.electronAPI) {
      analysisError.value = 'Electron API not available. Please run in Electron app.';
      return;
    }

    isAnalyzing.value = true;
    analysisError.value = null;

    // Open folder selection dialog
    const folderResult = await window.electronAPI.selectModsFolder();

    if (!folderResult.success) {
      if (folderResult.error !== 'No folder selected') {
        analysisError.value = folderResult.error;
      }
      isAnalyzing.value = false;
      return;
    }

    currentModsFolder.value = folderResult.path;

    // Analyze the entire folder
    const analysisResult = await window.electronAPI.analyzeModsFolder(folderResult.path);

    // Always try to process data, even if there were some analysis errors
    if (analysisResult.success || (analysisResult.data && Array.isArray(analysisResult.data))) {
      console.log('📊 Analysis result received:', analysisResult);
      console.log('📊 Data array length:', analysisResult.data?.length);

      // Check if data exists and is an array
      if (analysisResult.data && Array.isArray(analysisResult.data)) {
        const processedResults = analysisResult.data
          .filter((result: any) => result && (result.filePath || result.fileName)) // Filter out completely invalid results
          .map((result: any) => ({
          ...result,
          // Map filePath to fileName for UI compatibility
          fileName: result.filePath ? getFileName(result.filePath) : result.fileName || 'Unknown',
          // Ensure fileExtension is present
          fileExtension: result.fileExtension || (result.filePath ? getFileExtension(result.filePath) : '.package'),
          // Ensure fileSize is present
          fileSize: result.fileSize || 0,
          // Ensure all required properties for the UI
          resourceIntelligenceData: result.intelligence?.resourceIntelligence,
          dependencyData: result.intelligence?.dependencies,
          qualityAssessmentData: result.intelligence?.qualityAssessment,
          metadataConfidence: result.metadataConfidence || 0,
          processingTime: result.processingTime || 0,
          resourceCount: result.resourceCount || 0,
          // Add quality score from intelligence analysis
          qualityScore: result.intelligence?.qualityAssessment?.overallScore || 0,
          // Ensure metadata properties exist with safe fallbacks
          author: result.metadata?.author || result.author || 'Unknown',
          version: result.metadata?.version || result.version || '1.0',
          modName: result.metadata?.modName || result.modName || (result.fileName || 'Unknown Mod'),

          // Add basic category for UI display
          category: result.category || (result.fileExtension === '.ts4script' ? 'script' : 'package'),
          subcategory: result.subcategory || null,

          // Enhanced Content Analysis Fields
          casContent: result.intelligence?.resourceIntelligence?.contentAnalysis?.casContent || null,
          objectContent: result.intelligence?.resourceIntelligence?.contentAnalysis?.objectContent || null,
          objectClassification: result.intelligence?.resourceIntelligence?.contentAnalysis?.objectClassification || null,
          universalClassification: result.intelligence?.resourceIntelligence?.contentAnalysis?.universalClassification || null,

          // StringTable Analysis Fields
          stringTableData: result.intelligence?.stringTableData || null,
          actualModName: result.intelligence?.stringTableData?.modName || result.metadata?.modName || result.modName || null,
          actualDescription: result.intelligence?.stringTableData?.description || result.metadata?.description || null,
          extractedItemNames: result.intelligence?.stringTableData?.itemNames || [],
          hasStringTable: !!(result.intelligence?.stringTableData?.customStringCount > 0),

          // Enhanced metadata fields for better fallbacks (already set above)
        }));

        analysisResults.value = processedResults;
        console.log(`✅ Successfully processed ${analysisResults.value.length} mod files`);
        console.log('📊 First processed result:', analysisResults.value[0]);
        console.log('🔄 App.vue - analysisResults ASSIGNED:', {
          length: analysisResults.value.length,
          timestamp: new Date().toISOString(),
          firstMod: analysisResults.value[0]?.fileName
        });

        // Force Vue to re-render
        await nextTick();

        // Show warning if there were partial errors but still got some data
        if (!analysisResult.success && analysisResults.value.length > 0) {
          analysisError.value = `Analysis completed with some errors. Showing ${analysisResults.value.length} successfully analyzed mods. Check console for details.`;
        }
      } else {
        analysisError.value = 'No valid mod data found. Check that the folder contains .package or .ts4script files.';
      }
    } else {
      analysisError.value = analysisResult.error || 'Analysis failed. Check console for details.';
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'Failed to analyze mods folder';
  } finally {
    isAnalyzing.value = false;

    // Force another nextTick to ensure UI updates
    await nextTick();
  }
}

async function exportResults() {
  if (analysisResults.value.length === 0) return;

  if (!window.electronAPI) {
    analysisError.value = 'Electron API not available. Please run in Electron app.';
    return;
  }

  try {
    // For now, export as JSON - could add format selection dialog
    const exportResult = await window.electronAPI.exportResults(analysisResults.value, 'json');

    if (exportResult.success) {
      // Could show a success notification here
    } else {
      analysisError.value = exportResult.error;
    }
  } catch (error) {
    analysisError.value = error instanceof Error ? error.message : 'Failed to export results';
  }
}

// Helper function to get filename from path (replaces path.basename)
function getFileName(filePath: string): string {
  if (!filePath) return 'Unknown';
  return filePath.split(/[\\/]/).pop() || 'Unknown';
}

// Helper function to get file extension from path
function getFileExtension(filePath: string): string {
  if (!filePath) return '.package';
  const fileName = filePath.split(/[\\/]/).pop() || '';
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.substring(lastDot) : '.package';
}



// Auto-load default mods folder on startup
onMounted(async () => {
  // No auto-analysis - user must manually select and analyze folder
});

// Set up the analysis result handler (legacy support)
if (window.electronAPI?.onAnalysisResult) {
  window.electronAPI.onAnalysisResult((result: any) => {
    // This will be handled by the promise in handleAnalyzeRequested
  });
}
</script>

<style>
/* Import our enhanced design system */
@import './styles/simonitor-design-system.css';

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
  font-family: var(--font-family-sans);
}

.app-header {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
}

.app-title h1 {
  background: linear-gradient(135deg, var(--sims-blue), var(--plumbob-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-bold);
}

.app-main {
  flex: 1;
}

.app-footer {
  background-color: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  margin-top: auto;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--duration-150) var(--ease-out);
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--sims-blue);
  color: var(--text-inverse);
  border-color: var(--sims-blue);
}

.btn-primary:hover:not(:disabled) {
  background: var(--sims-blue-dark);
  border-color: var(--sims-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-strong);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
}

.badge-info {
  background: var(--info-bg);
  color: var(--info);
  border: 1px solid var(--info-border);
}

/* Card styles */
.card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

/* Container */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Utility classes */
.w-4 { width: 16px; }
.h-4 { height: 16px; }
.w-6 { width: 24px; }
.h-6 { height: 24px; }

.text-error {
  color: var(--error);
}

.text-muted {
  color: var(--text-secondary);
}

.welcome-content {
  max-width: 600px;
  margin: 0 auto;
}

.welcome-icon {
  color: var(--text-muted);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.feature-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.feature-item h4 {
  margin: 0 0 var(--spacing-sm) 0;
}

.feature-item p {
  margin: 0;
}

.max-w-md {
  max-width: 28rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.text-error {
  color: var(--error-color);
}

.loading-content .loading {
  border-width: 3px;
}

@media (max-width: 768px) {
  .app-title h1 {
    font-size: 1.5rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .feature-item {
    padding: var(--spacing-md);
  }
}
</style>