{"name": "simonitor", "version": "1.0.0", "description": "A new, stable Sims 4 Package Analyzer.", "main": "out/main/index.js", "scripts": {"start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "test": "vitest"}, "keywords": ["sims4", "mod-manager", "analyzer"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^24.0.14", "@vitejs/plugin-vue": "^6.0.0", "electron": "^31.0.2", "electron-vite": "^4.0.0", "typescript": "^5.4.5", "vite": "^7.0.4", "vitest": "^3.2.4"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@s4tk/models": "^0.6.2", "@tanstack/vue-virtual": "^3.13.12", "@vueuse/core": "^13.5.0", "vue": "^3.5.17", "vue-virtual-scroller": "^2.0.0-beta.8"}}