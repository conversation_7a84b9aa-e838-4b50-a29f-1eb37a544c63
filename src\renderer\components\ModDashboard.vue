<template>
  <div class="mod-dashboard">
    <!-- Modern Header with Search and Filters -->
    <header class="dashboard-header">
      <div class="header-content">
        <!-- Search Bar -->
        <div class="search-section">
          <div class="search-input-wrapper">
            <MagnifyingGlassIcon class="search-icon" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search your mod collection..."
              class="search-input"
              aria-label="Search mods"
            />
            <button
              v-if="searchQuery"
              @click="clearSearch"
              class="search-clear"
              aria-label="Clear search"
            >
              <XMarkIcon />
            </button>
          </div>
        </div>

        <!-- Filters and Controls -->
        <div class="header-controls">
          <div class="filter-section">
            <select v-model="selectedFileTypeFilter" class="filter-select" aria-label="Filter by file type">
              <option value="">All Types</option>
              <option value=".package">Package Files</option>
              <option value=".ts4script">Script Files</option>
            </select>

            <select v-model="selectedQualityFilter" class="filter-select" aria-label="Filter by quality">
              <option value="">All Quality</option>
              <option value="excellent">Excellent (90-100)</option>
              <option value="good">Good (70-89)</option>
              <option value="fair">Fair (50-69)</option>
              <option value="poor">Poor (0-49)</option>
            </select>

            <select v-model="selectedSortOption" class="filter-select" aria-label="Sort options">
              <option value="name">Sort by Name</option>
              <option value="quality">Sort by Quality</option>
              <option value="size">Sort by Size</option>
              <option value="author">Sort by Author</option>
            </select>
          </div>

          <!-- Results Info and Thumbnail Size Controls -->
          <div class="header-info">
            <div class="results-info">
              <span class="results-count">{{ filteredMods?.length || 0 }}</span>
              <span class="results-text">mods</span>
              <span v-if="hasActiveFilters" class="results-filtered">(filtered)</span>
            </div>

            <div class="thumbnail-size-controls">
              <button
                @click="thumbnailSize = 'small'"
                :class="{ active: thumbnailSize === 'small' }"
                class="size-btn"
                title="Small thumbnails"
                aria-label="Small thumbnail size"
              >
                <Squares2X2Icon />
              </button>
              <button
                @click="thumbnailSize = 'medium'"
                :class="{ active: thumbnailSize === 'medium' }"
                class="size-btn"
                title="Medium thumbnails"
                aria-label="Medium thumbnail size"
              >
                <Square3Stack3DIcon />
              </button>
              <button
                @click="thumbnailSize = 'large'"
                :class="{ active: thumbnailSize === 'large' }"
                class="size-btn"
                title="Large thumbnails"
                aria-label="Large thumbnail size"
              >
                <RectangleStackIcon />
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
    






    <!-- Main Content Area -->
    <main class="dashboard-main">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p class="loading-text">Analyzing mods with advanced intelligence...</p>
      </div>

      <!-- Thumbnail Grid (Primary View) -->
      <div v-else-if="filteredMods.length > 0" class="thumbnail-gallery">
        <div
          class="thumbnail-grid"
          :class="`thumbnail-grid--${thumbnailSize}`"
          role="grid"
          aria-label="Mod collection"
        >
          <article
            v-for="(mod, index) in paginatedMods"
            :key="mod.fileName || index"
            class="thumbnail-item"
            role="gridcell"
            :tabindex="0"
            @click="openModDetails(mod)"
            @keydown.enter="openModDetails(mod)"
            @keydown.space.prevent="openModDetails(mod)"
            :aria-label="`${getModDisplayName(mod)} by ${mod?.author || 'Unknown'}`"
          >
            <div class="thumbnail-container">
              <!-- Thumbnail Image -->
              <div class="thumbnail-image">
                <img
                  v-if="mod?.thumbnailUrl"
                  :src="mod.thumbnailUrl"
                  :alt="`${getModDisplayName(mod)} thumbnail`"
                  class="thumbnail-img"
                  loading="lazy"
                  @error="handleThumbnailError(mod)"
                />
                <div v-else class="thumbnail-fallback">
                  <component :is="getCategoryIcon(mod)" class="fallback-icon" />
                </div>
              </div>

              <!-- Thumbnail Overlay -->
              <div class="thumbnail-overlay">
                <h3 class="thumbnail-title">{{ getModDisplayName(mod) }}</h3>
                <p v-if="mod?.author" class="thumbnail-author">by {{ mod.author }}</p>
              </div>

              <!-- Quality Badge -->
              <div v-if="getQualityScore(mod)" class="quality-badge" :class="getQualityClass(mod)">
                {{ getQualityScore(mod) }}
              </div>

              <!-- File Type Badge -->
              <div class="file-type-badge" :class="getFileTypeClass(mod)">
                {{ getFileTypeLabel(mod) }}
              </div>
            </div>
          </article>
        </div>

        <!-- Pagination -->
        <nav v-if="totalPages > 1" class="pagination" aria-label="Pagination navigation">
          <button
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="pagination-btn"
            aria-label="Previous page"
          >
            Previous
          </button>
          <span class="pagination-info">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <button
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="pagination-btn"
            aria-label="Next page"
          >
            Next
          </button>
        </nav>
      </div>

      <!-- Empty State -->
      <div v-else class="empty-state">
        <div class="empty-state__icon">
          <FolderOpenIcon />
        </div>
        <h2 class="empty-state__title">No mods found</h2>
        <p class="empty-state__description">
          {{ hasActiveFilters ?
            'Try adjusting your filters to see more results.' :
            'Start by analyzing your Sims 4 mods folder or load test data to explore the interface.' }}
        </p>
        <button v-if="hasActiveFilters" @click="clearAllFilters" class="empty-state__action">
          Clear All Filters
        </button>
      </div>
    </main>

    <!-- Mod Details Modal -->
    <div v-if="selectedMod" class="mod-details-modal" @click.self="closeModDetails">
      <div class="mod-details-content" role="dialog" aria-modal="true" :aria-label="`Details for ${getModDisplayName(selectedMod)}`">
        <header class="mod-details-header">
          <div class="mod-details-title-section">
            <h2 class="mod-details-title">{{ getModDisplayName(selectedMod) }}</h2>
            <p v-if="selectedMod?.author" class="mod-details-author">by {{ selectedMod.author }}</p>
          </div>
          <button @click="closeModDetails" class="mod-details-close" aria-label="Close details">
            <XMarkIcon />
          </button>
        </header>

        <div class="mod-details-body">
          <!-- Mod Image/Thumbnail -->
          <div class="mod-details-image">
            <img
              v-if="selectedMod?.thumbnailUrl"
              :src="selectedMod.thumbnailUrl"
              :alt="`${getModDisplayName(selectedMod)} preview`"
              class="mod-preview-img"
            />
            <div v-else class="mod-preview-fallback">
              <component :is="getCategoryIcon(selectedMod)" class="preview-fallback-icon" />
            </div>
          </div>

          <!-- Mod Information -->
          <div class="mod-details-info">
            <div class="mod-info-grid">
              <div class="mod-info-item">
                <span class="mod-info-label">File Name</span>
                <span class="mod-info-value">{{ selectedMod?.fileName || 'Unknown' }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">File Size</span>
                <span class="mod-info-value">{{ formatFileSize(selectedMod?.fileSize || 0) }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">File Type</span>
                <span class="mod-info-value">{{ getFileTypeLabel(selectedMod) }}</span>
              </div>
              <div v-if="selectedMod?.version" class="mod-info-item">
                <span class="mod-info-label">Version</span>
                <span class="mod-info-value">{{ selectedMod?.version || 'Unknown' }}</span>
              </div>
              <div v-if="getQualityScore(selectedMod)" class="mod-info-item">
                <span class="mod-info-label">Quality Score</span>
                <span class="mod-info-value">{{ getQualityScore(selectedMod) }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">Category</span>
                <span class="mod-info-value">{{ getModCategory(selectedMod) }}</span>
              </div>
            </div>

            <!-- Additional Details -->
            <div v-if="selectedMod?.resourceCount" class="mod-additional-info">
              <h3>Technical Details</h3>
              <p>Resources: {{ selectedMod?.resourceCount || 0 }}</p>
              <p v-if="selectedMod?.processingTime">Processing Time: {{ selectedMod.processingTime }}ms</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Debug Panel -->
    <div v-if="showDebugMode" class="debug-panel">
      <h4>Debug Information</h4>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, shallowRef, shallowReactive } from 'vue';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  Squares2X2Icon,
  Square3Stack3DIcon,
  RectangleStackIcon,
  FolderOpenIcon,
  // Category icons
  UserIcon,
  HomeIcon,
  CogIcon,
  CommandLineIcon,
  CubeIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps<{
  mods?: any[];
  isLoading?: boolean;
  showDebugMode?: boolean;
}>();

// Use computed for reactive props - ensure reactivity
const mods = computed(() => {
  const modsData = props.mods || [];
  console.log('🔄 ModDashboard computed mods:', modsData.length, 'props.mods:', props.mods);
  if (modsData.length > 0) {
    console.log('🔄 First mod:', modsData[0]);
  }
  return modsData;
});
const isLoading = computed(() => props.isLoading || false);
const showDebugMode = computed(() => props.showDebugMode || false);

// Watch for loading state changes
watch(() => props.isLoading, (newLoading, oldLoading) => {
  // Loading state changed - trigger reactivity
}, { immediate: true });

// Reactive state - using shallow reactivity for performance
const searchQuery = ref('');
const selectedFileTypeFilter = ref('');
const selectedQualityFilter = ref('');
const selectedSortOption = ref('name');
const currentPage = ref(1);
const itemsPerPage = ref(24); // Better for grid layouts

// Thumbnail-first specific state
const thumbnailSize = ref<'small' | 'medium' | 'large'>('medium');
const selectedMod = ref<any>(null);

// Use shallowReactive for collections that change frequently
const modThumbnails = shallowRef<any[]>([]);
const selectedMods = shallowReactive(new Set<string>());

// Computed properties
const totalMods = computed(() => {
  return mods.value?.length || 0;
});



// Use shallowRef for filtered results to optimize performance
const filteredMods = shallowRef<any[]>([]);

// Watch for changes and update filtered mods
watch(
  [mods, searchQuery, selectedFileTypeFilter, selectedQualityFilter, selectedSortOption],
  () => {
    console.log('🔍 Watch triggered - mods.value:', mods.value?.length, 'type:', typeof mods.value);
    if (!mods.value || !Array.isArray(mods.value)) {
      console.log('🔍 No mods or not array, setting filteredMods to empty');
      filteredMods.value = [];
      return;
    }

    let filtered = [...mods.value];

    // Search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(mod =>
        (mod.fileName && mod.fileName.toLowerCase().includes(query)) ||
        (mod.author && mod.author.toLowerCase().includes(query)) ||
        (mod.modName && mod.modName.toLowerCase().includes(query))
      );
    }

    // File type filter
    if (selectedFileTypeFilter.value) {
      filtered = filtered.filter(mod =>
        mod && mod.fileExtension === selectedFileTypeFilter.value
      );
    }

  // Quality filter
  if (selectedQualityFilter.value) {
    filtered = filtered.filter(mod => {
      if (!mod || typeof mod.qualityScore !== 'number') return false;
      const score = mod.qualityScore;
      switch (selectedQualityFilter.value) {
        case 'excellent': return score >= 90;
        case 'good': return score >= 70 && score < 90;
        case 'fair': return score >= 50 && score < 70;
        case 'poor': return score < 50;
        case 'high': return score >= 80;
        case 'medium': return score >= 60 && score < 80;
        case 'low': return score < 60;
        default: return true;
      }
    });
  }

    // Sort
    filtered.sort((a, b) => {
      switch (selectedSortOption.value) {
        case 'name':
          return (a.fileName || '').localeCompare(b.fileName || '');
        case 'quality':
          return (b.qualityScore || 0) - (a.qualityScore || 0);
        case 'size':
          return (b.fileSize || 0) - (a.fileSize || 0);
        case 'author':
          return (a.author || '').localeCompare(b.author || '');
        case 'intelligence':
          return (a.intelligenceType || '').localeCompare(b.intelligenceType || '');
        default:
          return 0;
      }
    });

    filteredMods.value = filtered;
    console.log('🔍 Filtered mods updated:', filteredMods.value.length, filteredMods.value);
  },
  { immediate: true }
);

const totalPages = computed(() =>
  Math.ceil((filteredMods.value?.length || 0) / itemsPerPage.value)
);

// Use shallowRef for paginated results
const paginatedMods = shallowRef<any[]>([]);

// Watch for pagination changes
watch(
  [filteredMods, currentPage, itemsPerPage],
  () => {
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    paginatedMods.value = filteredMods.value?.slice(start, end) || [];
  },
  { immediate: true }
);

const hasActiveFilters = computed(() =>
  searchQuery.value ||
  selectedFileTypeFilter.value ||
  selectedQualityFilter.value
);

const debugInfo = computed(() => ({
  totalMods: totalMods.value,
  filteredMods: filteredMods.value?.length || 0,
  currentFilters: {
    search: searchQuery.value,
    fileType: selectedFileTypeFilter.value,
    quality: selectedQualityFilter.value
  },
  thumbnailSize: thumbnailSize.value,
  currentPage: currentPage.value
}));

// Watch for filtered mods changes (moved here after computed properties are defined)
watch(filteredMods, (newFiltered, oldFiltered) => {
  // Filtered mods changed - trigger reactivity
}, { immediate: true });

// Methods
const getModDisplayName = (mod: any): string => {
  // Priority 1: Use actual mod name from StringTable analysis if available
  if (mod.actualModName && mod.actualModName !== 'Unknown Mod') {
    return mod.actualModName;
  }

  // Priority 2: Use metadata mod name if available
  if (mod.modName && mod.modName !== 'Unknown Mod') {
    return mod.modName;
  }

  // Priority 3: Clean up the filename
  if (mod.fileName) {
    let name = mod.fileName
      .replace(/\.(package|ts4script)$/i, '') // Remove file extension
      .replace(/\[.*?\]/g, '') // Remove brackets and content (like [Author])
      .replace(/\{.*?\}/g, '') // Remove curly braces and content
      .replace(/\(.*?\)/g, '') // Remove parentheses and content
      .replace(/_+/g, ' ') // Replace underscores with spaces
      .replace(/-+/g, ' ') // Replace dashes with spaces
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();

    // Clean up common prefixes/suffixes
    name = name
      .replace(/^(mod|package|script|cc|custom|content)\s+/i, '') // Remove common prefixes
      .replace(/\s+(mod|package|script|cc|custom|content)$/i, '') // Remove common suffixes
      .replace(/^(the|a|an)\s+/i, '') // Remove articles at start
      .trim();

    // If we still have a meaningful name, format it nicely
    if (name && name.length > 0 && name !== 'Unknown') {
      // Handle special cases like "newemotionaltraits - arroganttrait"
      if (name.includes(' - ')) {
        return name.split(' - ')
          .map(part => part.split(' ')
            .map(word => word && word.length > 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : '')
            .filter(word => word.length > 0)
            .join(' '))
          .filter(part => part.length > 0)
          .join(' - ');
      } else {
        // Capitalize first letter of each word
        return name.split(' ')
          .map(word => word && word.length > 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : '')
          .filter(word => word.length > 0)
          .join(' ');
      }
    }
  }

  return 'Unknown Mod';
};

const getQualityScore = (mod: any): string => {
  if (!mod) return '';
  const score = mod.qualityAssessmentData?.overallScore || mod.qualityScore;
  if (typeof score === 'number') {
    // Quality scores are already in 0-100 range from QualityAssessmentAnalyzer
    const percentage = Math.round(Math.max(0, Math.min(100, score)));
    return `${percentage}%`;
  }
  return '';
};

const getQualityClass = (mod: any): string => {
  if (!mod) return 'quality-poor';
  const score = mod.qualityAssessmentData?.overallScore || mod.qualityScore || 0;
  if (score >= 0.8) return 'quality-excellent';
  if (score >= 0.6) return 'quality-good';
  if (score >= 0.4) return 'quality-fair';
  return 'quality-poor';
};

const getCategoryIcon = (mod: any) => {
  // Return appropriate icon component based on mod category
  // For now, return a default icon - you can expand this based on categories
  return 'CubeIcon'; // Default icon
};

const getFileTypeClass = (mod: any): string => {
  if (!mod) return 'file-type-package';
  const extension = mod.fileExtension || '.package';
  return extension === '.ts4script' ? 'file-type-script' : 'file-type-package';
};

const getFileTypeLabel = (mod: any): string => {
  if (!mod) return 'Package';
  const extension = mod.fileExtension || '.package';
  return extension === '.ts4script' ? 'Script' : 'Package';
};

const getModCategory = (mod: any): string => {
  if (!mod) return 'Unknown';
  // Try to get category from various sources
  if (mod.universalClassification?.category) return mod.universalClassification.category;
  if (mod.objectClassification?.category) return mod.objectClassification.category;
  if (mod.category) return mod.category;
  return 'Unknown';
};

const clearSearch = () => {
  searchQuery.value = '';
};

const clearAllFilters = () => {
  searchQuery.value = '';
  selectedFileTypeFilter.value = '';
  selectedQualityFilter.value = '';
  currentPage.value = 1;
};

const handleSort = (field: string) => {
  selectedSortOption.value = field;
  currentPage.value = 1;
};

const formatBytes = (bytes: number | null | undefined): string => {
  if (!bytes || bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const formatFileSize = (bytes: number | null | undefined): string => {
  return formatBytes(bytes);
};

// Modal and interaction methods
const openModDetails = (mod: any) => {
  if (mod) {
    selectedMod.value = mod;
  }
};

const closeModDetails = () => {
  selectedMod.value = null;
};

const handleThumbnailError = (mod: any) => {
  console.warn('Failed to load thumbnail for:', mod?.fileName || 'Unknown mod');
  // Could set a fallback thumbnail URL here
};

// Watch for filter changes to reset pagination
const resetPagination = () => {
  currentPage.value = 1;
};

// Thumbnail methods
const openImagePreview = (thumbnail: any) => {
  const index = modThumbnails.value.findIndex(t => t.id === thumbnail.id);
  if (index !== -1) {
    previewThumbnailIndex.value = index;
    showImagePreview.value = true;
  }
};

const closeImagePreview = () => {
  showImagePreview.value = false;
};

// Development helper: Test null safety on component mount
onMounted(() => {
  if (import.meta.env.DEV) {
    console.log('🧪 ModDashboard: Testing null safety on mount...');

    const testCases = [
      null,
      undefined,
      {},
      { fileName: null },
      { fileName: undefined },
      { fileName: "", author: null },
      { qualityScore: null, fileSize: undefined },
      { fileName: "test.package", author: "", version: null, qualityScore: NaN }
    ];

    let allTestsPassed = true;

    testCases.forEach((testMod, index) => {
      try {
        getModDisplayName(testMod);
        getQualityScore(testMod);
        getQualityClass(testMod);
        getFileTypeLabel(testMod);
        getFileTypeClass(testMod);
        getModCategory(testMod);
        formatFileSize(testMod?.fileSize);
      } catch (error) {
        console.error(`❌ Null safety test failed for case ${index}:`, error);
        allTestsPassed = false;
      }
    });

    if (allTestsPassed) {
      console.log('✅ All null safety tests passed!');
    }
  }
});

const showModDetails = (thumbnail: any) => {
  if (!thumbnail || !thumbnail.modFileName) return;

  // Find the corresponding mod and show its details
  const mod = mods.value?.find(m => m && m.fileName === thumbnail.modFileName);
  if (mod) {
    // Could emit an event or show a modal with mod details
    console.log('Show mod details for:', mod);
  }
};

const onThumbnailLoad = (thumbnail: any) => {
  // Handle successful thumbnail load
  console.log('Thumbnail loaded:', thumbnail.modFileName);
};

const onThumbnailError = (thumbnail: any) => {
  // Handle thumbnail load error
  console.warn('Failed to load thumbnail for:', thumbnail.modFileName);
};

const extractThumbnails = async () => {
  if (!mods.value || mods.value.length === 0) return;

  isExtractingThumbnails.value = true;
  thumbnailProgress.value = 0;
  modThumbnails.value = [];

  try {
    // Import the thumbnail extraction service
    const { ThumbnailExtractionService } = await import('../../services/visual/ThumbnailExtractionService');

    const totalMods = mods.value.length;
    let processedMods = 0;

    for (const mod of mods.value) {
      try {
        // For now, create mock thumbnails since we don't have actual file buffers
        const mockThumbnail = {
          id: `thumb_${mod.fileName || 'unknown'}_${Date.now()}`,
          modFileName: mod.fileName || 'unknown',
          resourceType: 'mock',
          resourceKey: 'mock_key',
          imageData: createMockThumbnailData(mod),
          format: 'svg' as const,
          width: 200,
          height: 200,
          category: mod.category || 'unknown',
          subcategory: mod.subcategory || null,
          confidence: 0.8,
          extractionMethod: 'fallback_icon' as const,
          fileSize: mod.fileSize || 0,
          isHighQuality: false,
          isFallback: true
        };

        modThumbnails.value.push(mockThumbnail);

      } catch (error) {
        console.warn(`Failed to extract thumbnail for ${mod?.fileName || 'unknown mod'}:`, error);
      }

      processedMods++;
      thumbnailProgress.value = (processedMods / totalMods) * 100;
    }

  } catch (error) {
    console.error('Error extracting thumbnails:', error);
  } finally {
    isExtractingThumbnails.value = false;
  }
};

const createMockThumbnailData = (mod: any): string => {
  const category = mod.category || 'unknown';
  const icons: Record<string, string> = {
    'cas': '👕',
    'objects': '🪑',
    'script': '⚙️',
    'tuning': '🔧',
    'unknown': '📦'
  };

  const icon = icons[category] || icons.unknown;
  const color = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'][Math.floor(Math.random() * 5)];

  const svg = `<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${color}" rx="8"/>
    <text x="50%" y="40%" text-anchor="middle" font-size="48">${icon}</text>
    <text x="50%" y="70%" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="12">
      ${category.toUpperCase()}
    </text>
  </svg>`;

  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

// Note: Thumbnail extraction is handled automatically when mods are loaded

// Lifecycle
onMounted(() => {
  // Any initialization logic
});
</script>

<style scoped>
.mod-dashboard {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* Main Dashboard Content */



/* Controls */
.dashboard-controls {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.search-section {
  display: flex;
  justify-content: center;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
}

.search-input {
  width: 100%;
  height: 56px;
  padding: 0 var(--space-12) 0 var(--space-12);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-full);
  background: var(--bg-elevated);
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  transition: all var(--duration-200) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

.search-input::placeholder {
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
}

.search-input:focus {
  outline: none;
  border-color: var(--plumbob-green);
  box-shadow: 0 0 0 4px var(--plumbob-green-bg), var(--shadow-md);
  background: var(--bg-primary);
}

.search-clear {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.filter-section {
  display: flex;
  gap: var(--space-4);
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.filter-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.filter-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  font-size: var(--text-sm);
  color: var(--text-primary);
  min-width: 150px;
}

.filter-clear-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.filter-clear-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--border-strong);
}

/* Results */
.results-summary {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-summary__info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.results-count {
  font-weight: var(--font-bold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
}

.results-text {
  color: var(--text-secondary);
}

.results-filtered {
  color: var(--text-accent);
  font-weight: var(--font-medium);
}

.view-options {
  display: flex;
  gap: var(--space-1);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-1);
}

.view-option {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.view-option:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.view-option.active {
  background: var(--sims-blue);
  color: var(--text-inverse);
}

.view-option svg {
  width: 16px;
  height: 16px;
}

/* Content */
.mod-results {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-6) var(--space-6);
}

.mod-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--space-6);
}

.mod-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.mod-table-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

/* Loading */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--sims-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.empty-state__icon {
  width: 64px;
  height: 64px;
  color: var(--text-tertiary);
  margin-bottom: var(--space-4);
}

.empty-state__title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.empty-state__description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0 0 var(--space-6) 0;
  max-width: 400px;
}

.empty-state__action {
  padding: var(--space-3) var(--space-6);
  background: var(--sims-blue);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.empty-state__action:hover {
  background: var(--sims-blue-dark);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }

  .stat-card {
    padding: var(--space-4);
  }

  .stat-card__value {
    font-size: var(--text-3xl);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: var(--space-4);
  }

  .dashboard-controls {
    padding: var(--space-4);
  }

  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-2);
  }

  .stat-card {
    padding: var(--space-3);
  }

  .stat-card__value {
    font-size: var(--text-2xl);
  }

  .stat-card__label {
    font-size: 10px;
  }

  .search-input-wrapper {
    max-width: none;
  }

  .search-input {
    height: 48px;
    font-size: var(--text-base);
  }

  .filter-section {
    flex-direction: column;
    gap: var(--space-3);
  }

  .filter-group {
    flex-direction: column;
    gap: var(--space-2);
  }

  .mod-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .results-summary {
    padding: 0 var(--space-4) var(--space-3) var(--space-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .view-options {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: var(--space-3);
  }

  .dashboard-title__main {
    font-size: var(--text-2xl);
  }

  .dashboard-title__subtitle {
    font-size: var(--text-sm);
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: var(--space-2);
  }

  .stat-card__value {
    font-size: var(--text-xl);
  }

  .search-input {
    height: 44px;
    padding: 0 var(--space-10) 0 var(--space-10);
  }

  .search-icon {
    left: var(--space-2);
    width: 18px;
    height: 18px;
  }

  .mod-results {
    padding: 0 var(--space-3) var(--space-3) var(--space-3);
  }
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-4);
  margin-top: var(--space-8);
}

.pagination-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}
</style>
